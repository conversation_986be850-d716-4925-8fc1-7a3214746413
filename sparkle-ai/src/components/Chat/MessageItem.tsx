import React, { useState } from 'react';
import { ChatMessage } from '../../types/chat';
import { formatTime, clipboard } from '../../utils/helpers';
import Button from '../Common/Button';

interface MessageItemProps {
  message: ChatMessage;
  onRegenerate?: () => void;
  onEdit?: (content: string) => void;
  onDelete?: () => void;
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  onRegenerate,
  onEdit,
  onDelete
}) => {
  const [showActions, setShowActions] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(message.content);

  const isUser = message.role === 'user';
  const isAssistant = message.role === 'assistant';

  const handleCopy = async () => {
    const success = await clipboard.write(message.content);
    if (success) {
      // TODO: 显示复制成功提示
    }
  };

  const handleEdit = () => {
    if (isEditing) {
      if (editContent.trim() !== message.content && onEdit) {
        onEdit(editContent.trim());
      }
      setIsEditing(false);
    } else {
      setIsEditing(true);
    }
  };

  const handleCancelEdit = () => {
    setEditContent(message.content);
    setIsEditing(false);
  };

  return (
    <div
      className={`flex ${isUser ? 'justify-end' : 'justify-start'} group`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      <div className={`max-w-3xl ${isUser ? 'order-2' : 'order-1'}`}>
        {/* 消息头部 */}
        <div className={`flex items-center mb-1 ${isUser ? 'justify-end' : 'justify-start'}`}>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500 dark:text-gray-400">
              {isUser ? '你' : 'AI'}
            </span>
            {message.model_used && (
              <span className="text-xs text-gray-400 dark:text-gray-500">
                ({message.model_used})
              </span>
            )}
            <span className="text-xs text-gray-400 dark:text-gray-500">
              {formatTime(message.timestamp)}
            </span>
          </div>
        </div>

        {/* 消息内容 */}
        <div
          className={`p-4 rounded-lg ${
            isUser
              ? 'bg-blue-600 text-white'
              : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white'
          }`}
        >
          {isEditing ? (
            <div className="space-y-3">
              <textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className={`w-full p-2 rounded border resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 ${
                  isUser
                    ? 'bg-primary-500 text-white placeholder-primary-200 border-primary-400'
                    : 'bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white border-gray-300 dark:border-gray-600'
                }`}
                rows={Math.max(3, editContent.split('\n').length)}
                autoFocus
              />
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant={isUser ? 'secondary' : 'primary'}
                  onClick={handleEdit}
                >
                  保存
                </Button>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={handleCancelEdit}
                >
                  取消
                </Button>
              </div>
            </div>
          ) : (
            <div className="whitespace-pre-wrap break-words">
              {message.content}
            </div>
          )}

          {/* 工具调用结果 */}
          {message.tool_calls && message.tool_calls.length > 0 && (
            <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
              <div className="text-sm opacity-75">
                <div className="font-medium mb-2">工具调用:</div>
                {message.tool_calls.map((toolCall) => (
                  <div key={toolCall.id} className="mb-2 p-2 bg-black/10 dark:bg-white/10 rounded">
                    <div className="font-mono text-xs">
                      {toolCall.tool_name}
                    </div>
                    {toolCall.result && (
                      <div className="text-xs mt-1 opacity-75">
                        {toolCall.result.result}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        {showActions && !isEditing && (
          <div className={`flex items-center space-x-1 mt-2 ${isUser ? 'justify-end' : 'justify-start'}`}>
            <Button
              size="sm"
              variant="ghost"
              onClick={handleCopy}
              className="opacity-0 group-hover:opacity-100 transition-opacity"
            >
              📋
            </Button>
            {onEdit && (
              <Button
                size="sm"
                variant="ghost"
                onClick={handleEdit}
                className="opacity-0 group-hover:opacity-100 transition-opacity"
              >
                ✏️
              </Button>
            )}
            {isAssistant && onRegenerate && (
              <Button
                size="sm"
                variant="ghost"
                onClick={onRegenerate}
                className="opacity-0 group-hover:opacity-100 transition-opacity"
              >
                🔄
              </Button>
            )}
            {onDelete && (
              <Button
                size="sm"
                variant="ghost"
                onClick={onDelete}
                className="opacity-0 group-hover:opacity-100 transition-opacity text-red-500 hover:text-red-600"
              >
                🗑️
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MessageItem;
