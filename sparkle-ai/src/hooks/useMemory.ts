import { useState, useCallback, useEffect } from 'react';
import { memoryApi, handleApiError } from '../utils/api';
import { Memory } from '../types/chat';

export const useMemory = () => {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMemory, setSelectedMemory] = useState<Memory | null>(null);

  // 搜索记忆
  const searchMemories = useCallback(async (query: string, limit?: number) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const results = await memoryApi.searchMemories(query, limit);
      setMemories(results);
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to search memories:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 添加新记忆
  const addMemory = useCallback(async (content: string, importance: number = 1) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const newMemory = await memoryApi.addMemory(content, importance);
      setMemories(prev => [newMemory, ...prev]);
      
      return newMemory;
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to add memory:', error);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 更新记忆
  const updateMemory = useCallback(async (id: string, content: string, importance: number) => {
    try {
      setIsLoading(true);
      setError(null);
      
      await memoryApi.updateMemory(id, content, importance);
      
      // 更新本地状态
      setMemories(prev => prev.map(memory => 
        memory.id === id 
          ? { ...memory, content, importance_level: importance, last_accessed: new Date() }
          : memory
      ));
      
      // 如果当前选中的记忆被更新，也要更新选中状态
      if (selectedMemory?.id === id) {
        setSelectedMemory(prev => prev ? {
          ...prev,
          content,
          importance_level: importance,
          last_accessed: new Date()
        } : null);
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to update memory:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedMemory]);

  // 删除记忆
  const deleteMemory = useCallback(async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);
      
      await memoryApi.deleteMemory(id);
      
      // 更新本地状态
      setMemories(prev => prev.filter(memory => memory.id !== id));
      
      // 如果删除的是当前选中的记忆，清空选中状态
      if (selectedMemory?.id === id) {
        setSelectedMemory(null);
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to delete memory:', error);
    } finally {
      setIsLoading(false);
    }
  }, [selectedMemory]);

  // 过滤记忆
  const filterMemories = useCallback((importanceLevel?: number) => {
    if (importanceLevel === undefined) {
      return memories;
    }
    return memories.filter(memory => memory.importance_level === importanceLevel);
  }, [memories]);

  // 处理搜索输入变化
  const handleSearchChange = useCallback((query: string) => {
    setSearchQuery(query);
    if (query.trim()) {
      searchMemories(query);
    } else {
      // 如果搜索为空，显示所有记忆
      searchMemories('', 50);
    }
  }, [searchMemories]);

  // 选择记忆
  const selectMemory = useCallback((memory: Memory) => {
    setSelectedMemory(memory);
    
    // 更新访问次数（这里只是本地更新，实际应该调用 API）
    setMemories(prev => prev.map(m => 
      m.id === memory.id 
        ? { ...m, access_count: m.access_count + 1, last_accessed: new Date() }
        : m
    ));
  }, []);

  // 清空选择
  const clearSelection = useCallback(() => {
    setSelectedMemory(null);
  }, []);

  // 获取重要性标签
  const getImportanceLabel = useCallback((level: number) => {
    switch (level) {
      case 1: return { text: '一般', color: 'blue' };
      case 2: return { text: '重要', color: 'yellow' };
      case 3: return { text: '关键', color: 'red' };
      default: return { text: '未知', color: 'gray' };
    }
  }, []);

  // 组件挂载时加载记忆
  useEffect(() => {
    searchMemories('', 50); // 加载最近的 50 条记忆
  }, [searchMemories]);

  return {
    // 状态
    memories,
    isLoading,
    error,
    searchQuery,
    selectedMemory,
    
    // 操作
    searchMemories,
    addMemory,
    updateMemory,
    deleteMemory,
    filterMemories,
    selectMemory,
    clearSelection,
    
    // 搜索相关
    handleSearchChange,
    
    // 工具方法
    getImportanceLabel,
    
    // 设置器
    setSearchQuery,
    setError,
  };
};
