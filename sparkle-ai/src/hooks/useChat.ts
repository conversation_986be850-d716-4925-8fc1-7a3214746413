import { useState, useCallback } from 'react';
import { useChatStore } from '../stores/chatStore';
import { useSettingsStore } from '../stores/settingsStore';
import { chatApi, handleApiError } from '../utils/api';
import { generateId } from '../utils/helpers';
import { ChatMessage } from '../types/chat';

export const useChat = () => {
  const {
    messages,
    currentSession,
    isLoading,
    error,
    addMessage,
    setLoading,
    setError,
    createNewSession,
    setCurrentSession
  } = useChatStore();

  const { settings } = useSettingsStore();
  const [inputValue, setInputValue] = useState('');

  // 发送消息
  const sendMessage = useCallback(async (content: string) => {
    if (!content.trim() || isLoading) return;

    try {
      setLoading(true);
      setError(null);

      // 如果没有当前会话，创建新会话
      if (!currentSession) {
        const newSession = createNewSession();
        setCurrentSession(newSession);
      }

      // 创建用户消息
      const userMessage: ChatMessage = {
        id: generateId(),
        role: 'user',
        content: content.trim(),
        timestamp: new Date(),
        model_used: undefined,
        tool_calls: undefined,
        rendered_content: undefined
      };

      // 添加用户消息到 store
      addMessage(userMessage);

      // 调用 API 发送消息
      const response = await chatApi.sendMessage(
        content.trim(),
        settings.ai_settings.default_model
      );

      // 创建 AI 响应消息
      const assistantMessage: ChatMessage = {
        id: generateId(),
        role: 'assistant',
        content: response.message,
        timestamp: new Date(),
        model_used: settings.ai_settings.default_model,
        tool_calls: response.tool_results?.map(result => ({
          id: generateId(),
          tool_name: result.tool_name,
          parameters: {},
          result
        })),
        rendered_content: undefined
      };

      // 添加 AI 响应到 store
      addMessage(assistantMessage);

    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to send message:', error);
    } finally {
      setLoading(false);
    }
  }, [
    isLoading,
    currentSession,
    settings.ai_settings.default_model,
    addMessage,
    setLoading,
    setError,
    createNewSession,
    setCurrentSession
  ]);

  // 清空聊天历史
  const clearChat = useCallback(async () => {
    try {
      await chatApi.clearChatHistory();
      // 这里应该清空 store 中的消息，但我们的 store 还没有这个方法
      // clearMessages();
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to clear chat:', error);
    }
  }, [setError]);

  // 重新生成响应
  const regenerateResponse = useCallback(async () => {
    if (messages.length === 0 || isLoading) return;

    // 找到最后一条用户消息
    const lastUserMessage = [...messages].reverse().find(msg => msg.role === 'user');
    if (!lastUserMessage) return;

    // 移除最后一条 AI 响应（如果存在）
    // TODO: 实现移除最后一条消息的功能

    // 重新发送最后一条用户消息
    await sendMessage(lastUserMessage.content);
  }, [messages, isLoading, sendMessage]);

  // 处理输入框回车事件
  const handleKeyPress = useCallback((event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter') {
      if (event.shiftKey) {
        // Shift+Enter 换行，不做任何处理
        return;
      } else if (event.ctrlKey || event.metaKey) {
        // Ctrl+Enter 或 Cmd+Enter 发送消息
        event.preventDefault();
        if (inputValue.trim()) {
          sendMessage(inputValue);
          setInputValue('');
        }
      }
    }
  }, [inputValue, sendMessage]);

  // 处理发送按钮点击
  const handleSend = useCallback(() => {
    if (inputValue.trim()) {
      sendMessage(inputValue);
      setInputValue('');
    }
  }, [inputValue, sendMessage]);

  return {
    // 状态
    messages,
    currentSession,
    isLoading,
    error,
    inputValue,
    
    // 操作
    sendMessage,
    clearChat,
    regenerateResponse,
    setInputValue,
    handleKeyPress,
    handleSend,
    
    // 设置
    maxTokens: settings.ai_settings.max_tokens,
    currentModel: settings.ai_settings.default_model,
  };
};
