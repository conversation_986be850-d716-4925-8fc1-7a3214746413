import { useCallback, useEffect } from 'react';
import { useSettingsStore } from '../stores/settingsStore';
import { settingsApi, handleApiError } from '../utils/api';
import { AppSettings } from '../types/api';

export const useSettings = () => {
  const {
    settings,
    isLoading,
    error,
    updateSettings,
    updateAISettings,
    updateUISettings,
    updateSecuritySettings,
    updatePerformanceSettings,
    updateSiYuanSettings,
    resetSettings,
    setLoading,
    setError
  } = useSettingsStore();

  // 从后端加载设置
  const loadSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const backendSettings = await settingsApi.getSettings();
      updateSettings(backendSettings);
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to load settings:', error);
    } finally {
      setLoading(false);
    }
  }, [updateSettings, setLoading, setError]);

  // 保存设置到后端
  const saveSettings = useCallback(async (newSettings?: AppSettings) => {
    try {
      setLoading(true);
      setError(null);
      
      const settingsToSave = newSettings || settings;
      await settingsApi.updateSettings(settingsToSave);
      
      if (newSettings) {
        updateSettings(newSettings);
      }
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to save settings:', error);
    } finally {
      setLoading(false);
    }
  }, [settings, updateSettings, setLoading, setError]);

  // 重置设置
  const handleResetSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      await settingsApi.resetSettings();
      resetSettings();
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to reset settings:', error);
    } finally {
      setLoading(false);
    }
  }, [resetSettings, setLoading, setError]);

  // 更新 AI 设置
  const handleUpdateAISettings = useCallback(async (aiSettings: Partial<AppSettings['ai_settings']>) => {
    const newSettings = {
      ...settings,
      ai_settings: { ...settings.ai_settings, ...aiSettings }
    };
    
    updateAISettings(aiSettings);
    await saveSettings(newSettings);
  }, [settings, updateAISettings, saveSettings]);

  // 更新 UI 设置
  const handleUpdateUISettings = useCallback(async (uiSettings: Partial<AppSettings['ui_settings']>) => {
    const newSettings = {
      ...settings,
      ui_settings: { ...settings.ui_settings, ...uiSettings }
    };
    
    updateUISettings(uiSettings);
    await saveSettings(newSettings);
  }, [settings, updateUISettings, saveSettings]);

  // 更新安全设置
  const handleUpdateSecuritySettings = useCallback(async (securitySettings: Partial<AppSettings['security_settings']>) => {
    const newSettings = {
      ...settings,
      security_settings: { ...settings.security_settings, ...securitySettings }
    };
    
    updateSecuritySettings(securitySettings);
    await saveSettings(newSettings);
  }, [settings, updateSecuritySettings, saveSettings]);

  // 更新性能设置
  const handleUpdatePerformanceSettings = useCallback(async (performanceSettings: Partial<AppSettings['performance_settings']>) => {
    const newSettings = {
      ...settings,
      performance_settings: { ...settings.performance_settings, ...performanceSettings }
    };
    
    updatePerformanceSettings(performanceSettings);
    await saveSettings(newSettings);
  }, [settings, updatePerformanceSettings, saveSettings]);

  // 更新思源笔记设置
  const handleUpdateSiYuanSettings = useCallback(async (siyuanSettings: Partial<AppSettings['siyuan_settings']>) => {
    const newSettings = {
      ...settings,
      siyuan_settings: { ...settings.siyuan_settings, ...siyuanSettings }
    };
    
    updateSiYuanSettings(siyuanSettings);
    await saveSettings(newSettings);
  }, [settings, updateSiYuanSettings, saveSettings]);

  // 测试思源笔记连接
  const testSiYuanConnection = useCallback(async (host?: string, port?: number, token?: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const testHost = host || settings.siyuan_settings.host;
      const testPort = port || settings.siyuan_settings.port;
      const testToken = token || settings.siyuan_settings.token;
      
      // TODO: 实现思源笔记连接测试
      // const isConnected = await siyuanApi.testConnection(testHost, testPort, testToken);
      const isConnected = true; // 临时返回 true
      
      return isConnected;
    } catch (error) {
      const errorMessage = handleApiError(error);
      setError(errorMessage);
      console.error('Failed to test SiYuan connection:', error);
      return false;
    } finally {
      setLoading(false);
    }
  }, [settings.siyuan_settings, setLoading, setError]);

  // 组件挂载时加载设置
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    // 状态
    settings,
    isLoading,
    error,
    
    // 操作
    loadSettings,
    saveSettings,
    resetSettings: handleResetSettings,
    
    // 分类更新方法
    updateAISettings: handleUpdateAISettings,
    updateUISettings: handleUpdateUISettings,
    updateSecuritySettings: handleUpdateSecuritySettings,
    updatePerformanceSettings: handleUpdatePerformanceSettings,
    updateSiYuanSettings: handleUpdateSiYuanSettings,
    
    // 特殊操作
    testSiYuanConnection,
  };
};
