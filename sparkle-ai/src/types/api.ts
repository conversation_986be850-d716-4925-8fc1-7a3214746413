// API 相关类型定义

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface AppSettings {
  ai_settings: AISettings;
  ui_settings: UISettings;
  security_settings: SecuritySettings;
  performance_settings: PerformanceSettings;
  siyuan_settings: SiYuanSettings;
}

export interface AISettings {
  default_model: string;
  temperature: number;
  max_tokens: number;
  conversation_window: number;
  system_prompt: string;
  tool_permissions: Record<string, boolean>;
  api_keys: Record<string, string>;
}

export interface UISettings {
  theme: 'light' | 'dark' | 'auto';
  font_size: number;
  language: string;
  hotkey: string;
  window_opacity: number;
  animations_enabled: boolean;
}

export interface SecuritySettings {
  tool_execution_enabled: boolean;
  command_whitelist: string[];
  command_blacklist: string[];
  max_execution_time: number;
  sandbox_mode: boolean;
}

export interface PerformanceSettings {
  memory_limit: number;
  cache_size: number;
  concurrent_requests: number;
  log_level: 'error' | 'warn' | 'info' | 'debug';
}

export interface SiYuanSettings {
  host: string;
  port: number;
  token: string;
  default_notebook?: string;
  auto_sync: boolean;
  sync_interval: number;
}

export interface SiYuanBlock {
  id: string;
  content: string;
  type: string;
  path: string;
  notebook: string;
  created: string;
  updated: string;
}

export interface SiYuanDocument {
  id: string;
  title: string;
  content: string;
  path: string;
  notebook: string;
  created: string;
  updated: string;
}

export interface SiYuanNotebook {
  id: string;
  name: string;
  icon: string;
  sort: number;
}

export interface SystemInfo {
  os: string;
  arch: string;
  version: string;
  memory_total: number;
  memory_available: number;
  cpu_count: number;
  uptime: number;
}

export interface UpdateInfo {
  available: boolean;
  version?: string;
  download_url?: string;
  release_notes?: string;
}
