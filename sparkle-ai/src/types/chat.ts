// 聊天相关类型定义

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  model_used?: string;
  tool_calls?: ToolCall[];
  rendered_content?: RenderedContent;
}

export interface RenderedContent {
  html: string;
  mermaid_diagrams: MermaidDiagram[];
  echarts_configs: EChartsConfig[];
  has_interactive_content: boolean;
}

export interface MermaidDiagram {
  id: string;
  definition: string;
  diagram_type: string;
}

export interface EChartsConfig {
  id: string;
  config: any;
  chart_type: string;
}

export interface ToolCall {
  id: string;
  tool_name: string;
  parameters: Record<string, any>;
  result?: ToolResult;
}

export interface ToolResult {
  tool_name: string;
  success: boolean;
  result: string;
  execution_time: number;
  error?: string;
}

export interface ChatResponse {
  message: string;
  tool_results?: ToolResult[];
  memories_used: Memory[];
  tokens_used?: number;
  cost?: number;
}

export interface Memory {
  id: string;
  content: string;
  summary?: string;
  importance_level: number;
  created_at: Date;
  last_accessed: Date;
  access_count: number;
  tags: string[];
}

export interface AIModel {
  id: string;
  name: string;
  provider: 'deepseek' | 'tongyi' | 'ollama';
  max_tokens: number;
  cost_per_token?: number;
  available: boolean;
}

export interface ChatSession {
  id: string;
  title: string;
  messages: ChatMessage[];
  created_at: Date;
  updated_at: Date;
  model_id: string;
}
