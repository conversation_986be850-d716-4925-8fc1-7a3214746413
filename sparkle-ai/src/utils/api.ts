import { invoke } from '@tauri-apps/api/core';
import { ApiResponse, AppSettings } from '../types/api';
import { ChatMessage, ChatResponse } from '../types/chat';

// 聊天相关 API
export const chatApi = {
  async sendMessage(message: string, model?: string): Promise<ChatResponse> {
    return await invoke<ChatResponse>('send_message', { message, model });
  },

  async getChatHistory(limit?: number): Promise<ChatMessage[]> {
    return await invoke<ChatMessage[]>('get_chat_history', { limit });
  },

  async clearChatHistory(): Promise<void> {
    return await invoke<void>('clear_chat_history');
  }
};

// 设置相关 API
export const settingsApi = {
  async getSettings(): Promise<AppSettings> {
    return await invoke<AppSettings>('get_settings');
  },

  async updateSettings(settings: AppSettings): Promise<void> {
    return await invoke<void>('update_settings', { settings });
  },

  async resetSettings(): Promise<void> {
    return await invoke<void>('reset_settings');
  }
};

// 工具相关 API
export const toolsApi = {
  async executeTool(toolName: string, params: Record<string, any>): Promise<any> {
    return await invoke('execute_tool', { toolName, params });
  },

  async getAvailableTools(): Promise<any[]> {
    return await invoke('get_available_tools');
  }
};

// 记忆管理 API
export const memoryApi = {
  async searchMemories(query: string, limit?: number): Promise<any[]> {
    return await invoke('search_memories', { query, limit });
  },

  async addMemory(content: string, importance: number): Promise<any> {
    return await invoke('add_memory', { content, importance });
  },

  async updateMemory(id: string, content: string, importance: number): Promise<void> {
    return await invoke('update_memory', { id, content, importance });
  },

  async deleteMemory(id: string): Promise<void> {
    return await invoke('delete_memory', { id });
  }
};

// 思源笔记 API
export const siyuanApi = {
  async createNote(title: string, content: string, notebook?: string): Promise<string> {
    return await invoke('siyuan_create_note', { title, content, notebook });
  },

  async searchNotes(query: string, limit?: number): Promise<any[]> {
    return await invoke('siyuan_search_notes', { query, limit });
  },

  async getNote(docId: string): Promise<any> {
    return await invoke('siyuan_get_note', { docId });
  },

  async updateNote(docId: string, content: string): Promise<void> {
    return await invoke('siyuan_update_note', { docId, content });
  },

  async deleteNote(docId: string): Promise<void> {
    return await invoke('siyuan_delete_note', { docId });
  },

  async testConnection(host: string, port: number, token: string): Promise<boolean> {
    return await invoke('siyuan_test_connection', { host, port, token });
  },

  async getNotebooks(): Promise<any[]> {
    return await invoke('siyuan_get_notebooks');
  }
};

// 系统信息 API
export const systemApi = {
  async getSystemInfo(): Promise<any> {
    return await invoke('get_system_info');
  },

  async checkUpdates(): Promise<any> {
    return await invoke('check_updates');
  }
};

// 错误处理工具
export const handleApiError = (error: any): string => {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  return '发生未知错误';
};

// API 响应包装器
export const apiWrapper = async <T>(
  apiCall: () => Promise<T>
): Promise<ApiResponse<T>> => {
  try {
    const data = await apiCall();
    return {
      success: true,
      data
    };
  } catch (error) {
    return {
      success: false,
      error: handleApiError(error)
    };
  }
};
