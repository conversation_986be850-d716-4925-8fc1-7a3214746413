// 应用常量定义

export const APP_NAME = 'Sparkle-AI';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = '智能系统助手';

// 主题相关
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
} as const;

// 语言相关
export const LANGUAGES = {
  ZH_CN: 'zh-CN',
  EN_US: 'en-US'
} as const;

// AI 模型相关
export const AI_MODELS = {
  DEEPSEEK_CHAT: 'deepseek-chat',
  DEEPSEEK_CODER: 'deepseek-coder',
  TONGYI_QWEN: 'qwen-turbo',
  OLLAMA_LLAMA: 'llama2'
} as const;

// 工具名称
export const TOOLS = {
  SAFE_SHELL: 'safe_shell',
  WEB_SEARCH: 'web_search',
  GAODE_MAP: 'gaode_map',
  CALENDAR_MANAGER: 'calendar_manager',
  SIYUAN_NOTES: 'siyuan_notes'
} as const;

// 消息角色
export const MESSAGE_ROLES = {
  USER: 'user',
  ASSISTANT: 'assistant',
  SYSTEM: 'system'
} as const;

// 日志级别
export const LOG_LEVELS = {
  ERROR: 'error',
  WARN: 'warn',
  INFO: 'info',
  DEBUG: 'debug'
} as const;

// 默认设置值
export const DEFAULT_VALUES = {
  TEMPERATURE: 0.7,
  MAX_TOKENS: 2000,
  CONVERSATION_WINDOW: 10,
  FONT_SIZE: 14,
  WINDOW_OPACITY: 0.95,
  MEMORY_LIMIT: 512, // MB
  CACHE_SIZE: 100, // MB
  CONCURRENT_REQUESTS: 3,
  MAX_EXECUTION_TIME: 30000, // 30秒
  SIYUAN_PORT: 6806,
  SYNC_INTERVAL: 300 // 5分钟
} as const;

// 快捷键
export const HOTKEYS = {
  QUICK_INPUT: 'Ctrl+Q',
  NEW_CHAT: 'Ctrl+N',
  CLEAR_CHAT: 'Ctrl+L',
  SETTINGS: 'Ctrl+,',
  TOGGLE_THEME: 'Ctrl+T'
} as const;

// 文件类型
export const FILE_TYPES = {
  TEXT: ['txt', 'md', 'json', 'xml', 'csv'],
  IMAGE: ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'],
  CODE: ['js', 'ts', 'jsx', 'tsx', 'py', 'java', 'cpp', 'c', 'rs', 'go']
} as const;

// API 端点
export const API_ENDPOINTS = {
  DEEPSEEK: 'https://api.deepseek.com/v1',
  TONGYI: 'https://dashscope.aliyuncs.com/api/v1',
  SIYUAN: '/api'
} as const;

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接错误，请检查网络设置',
  API_KEY_INVALID: 'API 密钥无效，请检查设置',
  MODEL_UNAVAILABLE: '模型暂时不可用，请稍后重试',
  TOOL_EXECUTION_FAILED: '工具执行失败',
  PERMISSION_DENIED: '权限不足，无法执行此操作',
  FILE_NOT_FOUND: '文件未找到',
  INVALID_INPUT: '输入格式不正确',
  TIMEOUT: '操作超时，请重试',
  UNKNOWN_ERROR: '发生未知错误'
} as const;

// 成功消息
export const SUCCESS_MESSAGES = {
  SETTINGS_SAVED: '设置已保存',
  MESSAGE_SENT: '消息已发送',
  FILE_UPLOADED: '文件上传成功',
  TOOL_EXECUTED: '工具执行成功',
  MEMORY_SAVED: '记忆已保存',
  SESSION_CREATED: '会话已创建',
  SESSION_DELETED: '会话已删除'
} as const;

// 正则表达式
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  PHONE: /^1[3-9]\d{9}$/,
  IP_ADDRESS: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  MARKDOWN_CODE_BLOCK: /```(\w+)?\n([\s\S]*?)\n```/g,
  MERMAID_BLOCK: /```mermaid\n([\s\S]*?)\n```/g,
  ECHARTS_BLOCK: /```echarts\n([\s\S]*?)\n```/g
} as const;

// 动画持续时间
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500
} as const;

// 尺寸常量
export const SIZES = {
  SIDEBAR_WIDTH: 280,
  HEADER_HEIGHT: 60,
  FOOTER_HEIGHT: 40,
  QUICK_INPUT_WIDTH: 600,
  QUICK_INPUT_HEIGHT: 400
} as const;

// Z-index 层级
export const Z_INDEX = {
  MODAL: 1000,
  DROPDOWN: 900,
  TOOLTIP: 800,
  OVERLAY: 700,
  HEADER: 600
} as const;
