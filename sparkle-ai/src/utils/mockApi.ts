// 模拟 API 实现，用于前端开发和测试

import { AppSettings } from '../types/api';
import { ChatMessage, ChatResponse, Memory } from '../types/chat';
import { generateId, storage } from './helpers';

// 模拟延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟聊天 API
export const mockChatApi = {
  async sendMessage(message: string, model?: string): Promise<ChatResponse> {
    await delay(1000 + Math.random() * 2000); // 1-3秒延迟
    
    // 模拟 AI 响应
    const responses = [
      `我理解您的问题："${message}"。这是一个很好的问题，让我来帮助您解答。`,
      `关于"${message}"，我可以为您提供以下信息和建议...`,
      `您提到的"${message}"确实很重要。根据我的理解，这里有几个要点需要注意...`,
      `感谢您的提问："${message}"。让我为您详细分析一下这个问题。`,
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    
    return {
      message: randomResponse,
      tool_results: undefined,
      memories_used: [],
      tokens_used: Math.floor(Math.random() * 100) + 50,
      cost: Math.random() * 0.01,
    };
  },

  async getChatHistory(limit?: number): Promise<ChatMessage[]> {
    await delay(500);
    
    // 从本地存储获取历史记录
    const history = storage.get<ChatMessage[]>('chat_history', []);
    const limitedHistory = limit ? history.slice(-limit) : history;
    
    return limitedHistory;
  },

  async clearChatHistory(): Promise<void> {
    await delay(300);
    storage.remove('chat_history');
  }
};

// 模拟设置 API
export const mockSettingsApi = {
  async getSettings(): Promise<AppSettings> {
    await delay(500);
    
    const defaultSettings: AppSettings = {
      ai_settings: {
        default_model: 'deepseek-chat',
        temperature: 0.7,
        max_tokens: 2000,
        conversation_window: 10,
        system_prompt: '你是 Sparkle-AI，一个智能系统助手。你可以帮助用户进行各种任务，包括文件操作、系统查询、网络搜索等。请用中文回答，保持友好和专业。',
        tool_permissions: {
          safe_shell: true,
          web_search: true,
          gaode_map: true,
          calendar_manager: false,
          siyuan_notes: true
        },
        api_keys: {}
      },
      ui_settings: {
        theme: 'auto',
        font_size: 14,
        language: 'zh-CN',
        hotkey: 'Ctrl+Q',
        window_opacity: 0.95,
        animations_enabled: true
      },
      security_settings: {
        tool_execution_enabled: true,
        command_whitelist: [
          'ls', 'cat', 'pwd', 'whoami', 'date', 'uptime',
          'df', 'free', 'ps', 'top', 'htop', 'uname',
          'find', 'grep', 'head', 'tail', 'wc', 'sort'
        ],
        command_blacklist: [
          'rm', 'rmdir', 'mv', 'cp', 'chmod', 'chown',
          'sudo', 'su', 'passwd', 'useradd', 'userdel',
          'systemctl', 'service', 'mount', 'umount',
          'fdisk', 'mkfs', 'dd', 'format'
        ],
        max_execution_time: 30000,
        sandbox_mode: false
      },
      performance_settings: {
        memory_limit: 512,
        cache_size: 100,
        concurrent_requests: 3,
        log_level: 'info'
      },
      siyuan_settings: {
        host: '127.0.0.1',
        port: 6806,
        token: '',
        default_notebook: undefined,
        auto_sync: false,
        sync_interval: 300
      }
    };
    
    return storage.get('app_settings', defaultSettings);
  },

  async updateSettings(settings: AppSettings): Promise<void> {
    await delay(500);
    storage.set('app_settings', settings);
  },

  async resetSettings(): Promise<void> {
    await delay(300);
    storage.remove('app_settings');
  }
};

// 模拟记忆 API
export const mockMemoryApi = {
  async searchMemories(query: string, limit?: number): Promise<Memory[]> {
    await delay(800);
    
    // 模拟记忆数据
    const mockMemories: Memory[] = [
      {
        id: '1',
        content: '用户喜欢使用暗色主题，认为这样对眼睛更友好',
        summary: '用户界面偏好',
        importance_level: 2,
        created_at: new Date(Date.now() - 86400000 * 2),
        last_accessed: new Date(Date.now() - 3600000),
        access_count: 5,
        tags: ['偏好', '界面', '主题']
      },
      {
        id: '2',
        content: '用户常用的工作目录是 ~/projects/website，主要进行前端开发工作',
        summary: '工作目录位置',
        importance_level: 3,
        created_at: new Date(Date.now() - 86400000 * 5),
        last_accessed: new Date(Date.now() - 86400000),
        access_count: 12,
        tags: ['工作', '目录', '开发']
      },
      {
        id: '3',
        content: '用户使用 VS Code 作为主要代码编辑器，安装了很多插件',
        summary: '开发工具偏好',
        importance_level: 1,
        created_at: new Date(Date.now() - 86400000 * 10),
        last_accessed: new Date(Date.now() - 86400000 * 3),
        access_count: 2,
        tags: ['开发', '工具', 'VSCode']
      },
      {
        id: '4',
        content: '用户对 React 和 TypeScript 比较熟悉，经常询问相关问题',
        summary: '技术栈偏好',
        importance_level: 2,
        created_at: new Date(Date.now() - 86400000 * 7),
        last_accessed: new Date(Date.now() - 7200000),
        access_count: 8,
        tags: ['技术', 'React', 'TypeScript']
      }
    ];
    
    // 简单的搜索过滤
    let filtered = mockMemories;
    if (query.trim()) {
      filtered = mockMemories.filter(memory =>
        memory.content.toLowerCase().includes(query.toLowerCase()) ||
        memory.summary?.toLowerCase().includes(query.toLowerCase()) ||
        memory.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
      );
    }
    
    // 应用限制
    if (limit) {
      filtered = filtered.slice(0, limit);
    }
    
    return filtered;
  },

  async addMemory(content: string, importance: number): Promise<Memory> {
    await delay(600);
    
    const newMemory: Memory = {
      id: generateId(),
      content,
      summary: content.length > 50 ? content.substring(0, 47) + '...' : content,
      importance_level: importance,
      created_at: new Date(),
      last_accessed: new Date(),
      access_count: 0,
      tags: [] // 在实际实现中，这里会使用 AI 生成标签
    };
    
    return newMemory;
  },

  async updateMemory(id: string, content: string, importance: number): Promise<void> {
    await delay(500);
    // 模拟更新操作
  },

  async deleteMemory(id: string): Promise<void> {
    await delay(400);
    // 模拟删除操作
  }
};
