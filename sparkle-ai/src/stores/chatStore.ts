import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { ChatMessage, ChatSession, AIModel } from '../types/chat';

interface ChatState {
  // 当前会话
  currentSession: ChatSession | null;
  // 所有会话
  sessions: ChatSession[];
  // 当前消息列表
  messages: ChatMessage[];
  // 可用模型
  availableModels: AIModel[];
  // 当前选中的模型
  currentModel: string;
  // 加载状态
  isLoading: boolean;
  // 错误信息
  error: string | null;
  
  // Actions
  setCurrentSession: (session: ChatSession | null) => void;
  addSession: (session: ChatSession) => void;
  updateSession: (sessionId: string, updates: Partial<ChatSession>) => void;
  deleteSession: (sessionId: string) => void;
  addMessage: (message: ChatMessage) => void;
  updateMessage: (messageId: string, updates: Partial<ChatMessage>) => void;
  deleteMessage: (messageId: string) => void;
  clearMessages: () => void;
  setAvailableModels: (models: AIModel[]) => void;
  setCurrentModel: (modelId: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // 工具方法
  createNewSession: (title?: string) => ChatSession;
  getSessionById: (sessionId: string) => ChatSession | undefined;
  getMessageById: (messageId: string) => ChatMessage | undefined;
}

export const useChatStore = create<ChatState>()(
  devtools(
    persist(
      (set, get) => ({
        currentSession: null,
        sessions: [],
        messages: [],
        availableModels: [],
        currentModel: 'deepseek-chat',
        isLoading: false,
        error: null,

        setCurrentSession: (session) => {
          set({ 
            currentSession: session,
            messages: session?.messages || []
          });
        },

        addSession: (session) => {
          set((state) => ({
            sessions: [...state.sessions, session]
          }));
        },

        updateSession: (sessionId, updates) => {
          set((state) => ({
            sessions: state.sessions.map(session =>
              session.id === sessionId ? { ...session, ...updates } : session
            ),
            currentSession: state.currentSession?.id === sessionId
              ? { ...state.currentSession, ...updates }
              : state.currentSession
          }));
        },

        deleteSession: (sessionId) => {
          set((state) => ({
            sessions: state.sessions.filter(session => session.id !== sessionId),
            currentSession: state.currentSession?.id === sessionId ? null : state.currentSession,
            messages: state.currentSession?.id === sessionId ? [] : state.messages
          }));
        },

        addMessage: (message) => {
          set((state) => {
            const newMessages = [...state.messages, message];
            
            // 更新当前会话的消息
            if (state.currentSession) {
              const updatedSession = {
                ...state.currentSession,
                messages: newMessages,
                updated_at: new Date()
              };
              
              return {
                messages: newMessages,
                currentSession: updatedSession,
                sessions: state.sessions.map(session =>
                  session.id === updatedSession.id ? updatedSession : session
                )
              };
            }
            
            return { messages: newMessages };
          });
        },

        updateMessage: (messageId, updates) => {
          set((state) => {
            const newMessages = state.messages.map(message =>
              message.id === messageId ? { ...message, ...updates } : message
            );
            
            // 更新当前会话的消息
            if (state.currentSession) {
              const updatedSession = {
                ...state.currentSession,
                messages: newMessages,
                updated_at: new Date()
              };
              
              return {
                messages: newMessages,
                currentSession: updatedSession,
                sessions: state.sessions.map(session =>
                  session.id === updatedSession.id ? updatedSession : session
                )
              };
            }
            
            return { messages: newMessages };
          });
        },

        deleteMessage: (messageId) => {
          set((state) => {
            const newMessages = state.messages.filter(message => message.id !== messageId);
            
            // 更新当前会话的消息
            if (state.currentSession) {
              const updatedSession = {
                ...state.currentSession,
                messages: newMessages,
                updated_at: new Date()
              };
              
              return {
                messages: newMessages,
                currentSession: updatedSession,
                sessions: state.sessions.map(session =>
                  session.id === updatedSession.id ? updatedSession : session
                )
              };
            }
            
            return { messages: newMessages };
          });
        },

        clearMessages: () => {
          set({ messages: [] });
        },

        setAvailableModels: (models) => {
          set({ availableModels: models });
        },

        setCurrentModel: (modelId) => {
          set({ currentModel: modelId });
        },

        setLoading: (loading) => {
          set({ isLoading: loading });
        },

        setError: (error) => {
          set({ error });
        },

        createNewSession: (title) => {
          const session: ChatSession = {
            id: crypto.randomUUID(),
            title: title || `会话 ${new Date().toLocaleString()}`,
            messages: [],
            created_at: new Date(),
            updated_at: new Date(),
            model_id: get().currentModel
          };
          
          get().addSession(session);
          return session;
        },

        getSessionById: (sessionId) => {
          return get().sessions.find(session => session.id === sessionId);
        },

        getMessageById: (messageId) => {
          return get().messages.find(message => message.id === messageId);
        }
      }),
      {
        name: 'chat-store',
        partialize: (state) => ({
          sessions: state.sessions,
          currentModel: state.currentModel,
          availableModels: state.availableModels
        })
      }
    ),
    { name: 'ChatStore' }
  )
);
