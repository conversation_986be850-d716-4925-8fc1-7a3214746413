import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';
import { AppSettings } from '../types/api';

interface SettingsState {
  settings: AppSettings;
  isLoading: boolean;
  error: string | null;
  
  // Actions
  updateSettings: (updates: Partial<AppSettings>) => void;
  updateAISettings: (updates: Partial<AppSettings['ai_settings']>) => void;
  updateUISettings: (updates: Partial<AppSettings['ui_settings']>) => void;
  updateSecuritySettings: (updates: Partial<AppSettings['security_settings']>) => void;
  updatePerformanceSettings: (updates: Partial<AppSettings['performance_settings']>) => void;
  updateSiYuanSettings: (updates: Partial<AppSettings['siyuan_settings']>) => void;
  resetSettings: () => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

const defaultSettings: AppSettings = {
  ai_settings: {
    default_model: 'deepseek-chat',
    temperature: 0.7,
    max_tokens: 2000,
    conversation_window: 10,
    system_prompt: '你是 Sparkle-AI，一个智能系统助手。你可以帮助用户进行各种任务，包括文件操作、系统查询、网络搜索等。请用中文回答，保持友好和专业。',
    tool_permissions: {
      safe_shell: true,
      web_search: true,
      gaode_map: true,
      calendar_manager: false,
      siyuan_notes: true
    },
    api_keys: {}
  },
  ui_settings: {
    theme: 'auto',
    font_size: 14,
    language: 'zh-CN',
    hotkey: 'Ctrl+Q',
    window_opacity: 0.95,
    animations_enabled: true
  },
  security_settings: {
    tool_execution_enabled: true,
    command_whitelist: [
      'ls', 'cat', 'pwd', 'whoami', 'date', 'uptime',
      'df', 'free', 'ps', 'top', 'htop', 'uname',
      'find', 'grep', 'head', 'tail', 'wc', 'sort'
    ],
    command_blacklist: [
      'rm', 'rmdir', 'mv', 'cp', 'chmod', 'chown',
      'sudo', 'su', 'passwd', 'useradd', 'userdel',
      'systemctl', 'service', 'mount', 'umount',
      'fdisk', 'mkfs', 'dd', 'format'
    ],
    max_execution_time: 30000, // 30秒
    sandbox_mode: false
  },
  performance_settings: {
    memory_limit: 512, // MB
    cache_size: 100, // MB
    concurrent_requests: 3,
    log_level: 'info'
  },
  siyuan_settings: {
    host: '127.0.0.1',
    port: 6806,
    token: '',
    default_notebook: undefined,
    auto_sync: false,
    sync_interval: 300 // 5分钟
  }
};

export const useSettingsStore = create<SettingsState>()(
  devtools(
    persist(
      (set, get) => ({
        settings: defaultSettings,
        isLoading: false,
        error: null,

        updateSettings: (updates) => {
          set((state) => ({
            settings: { ...state.settings, ...updates }
          }));
        },

        updateAISettings: (updates) => {
          set((state) => ({
            settings: {
              ...state.settings,
              ai_settings: { ...state.settings.ai_settings, ...updates }
            }
          }));
        },

        updateUISettings: (updates) => {
          set((state) => ({
            settings: {
              ...state.settings,
              ui_settings: { ...state.settings.ui_settings, ...updates }
            }
          }));
        },

        updateSecuritySettings: (updates) => {
          set((state) => ({
            settings: {
              ...state.settings,
              security_settings: { ...state.settings.security_settings, ...updates }
            }
          }));
        },

        updatePerformanceSettings: (updates) => {
          set((state) => ({
            settings: {
              ...state.settings,
              performance_settings: { ...state.settings.performance_settings, ...updates }
            }
          }));
        },

        updateSiYuanSettings: (updates) => {
          set((state) => ({
            settings: {
              ...state.settings,
              siyuan_settings: { ...state.settings.siyuan_settings, ...updates }
            }
          }));
        },

        resetSettings: () => {
          set({ settings: defaultSettings });
        },

        setLoading: (loading) => {
          set({ isLoading: loading });
        },

        setError: (error) => {
          set({ error });
        }
      }),
      {
        name: 'settings-store',
        partialize: (state) => ({ settings: state.settings })
      }
    ),
    { name: 'SettingsStore' }
  )
);
