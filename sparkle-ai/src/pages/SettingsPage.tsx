import React, { useState } from 'react';
import { useSettingsStore } from '../stores/settingsStore';

const SettingsPage: React.FC = () => {
  const { settings, updateAISettings, updateUISettings, updateSiYuanSettings } = useSettingsStore();
  const [activeTab, setActiveTab] = useState('ai');

  const tabs = [
    { id: 'ai', name: 'AI 设置', icon: '🤖' },
    { id: 'ui', name: '界面设置', icon: '🎨' },
    { id: 'security', name: '安全设置', icon: '🔒' },
    { id: 'siyuan', name: '思源笔记', icon: '📝' },
    { id: 'performance', name: '性能设置', icon: '⚡' }
  ];

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* 设置侧边栏 */}
      <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
        <div className="p-4">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">设置</h1>
        </div>
        
        <nav className="px-4 space-y-1">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`w-full text-left p-3 rounded-lg transition-colors ${
                activeTab === tab.id
                  ? 'bg-primary-100 dark:bg-primary-900 text-primary-700 dark:text-primary-300'
                  : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
              }`}
            >
              <span className="mr-3">{tab.icon}</span>
              {tab.name}
            </button>
          ))}
        </nav>
      </div>

      {/* 设置内容区域 */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          {activeTab === 'ai' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">AI 设置</h2>
              
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">模型配置</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      默认模型
                    </label>
                    <select
                      value={settings.ai_settings.default_model}
                      onChange={(e) => updateAISettings({ default_model: e.target.value })}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="deepseek-chat">DeepSeek Chat</option>
                      <option value="deepseek-coder">DeepSeek Coder</option>
                      <option value="qwen-turbo">通义千问 Turbo</option>
                      <option value="llama2">Ollama Llama2</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      温度参数: {settings.ai_settings.temperature}
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="2"
                      step="0.1"
                      value={settings.ai_settings.temperature}
                      onChange={(e) => updateAISettings({ temperature: parseFloat(e.target.value) })}
                      className="w-full"
                    />
                    <div className="flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-1">
                      <span>保守 (0)</span>
                      <span>创造性 (2)</span>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      最大 Token 数
                    </label>
                    <input
                      type="number"
                      min="100"
                      max="4000"
                      value={settings.ai_settings.max_tokens}
                      onChange={(e) => updateAISettings({ max_tokens: parseInt(e.target.value) })}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      系统提示词
                    </label>
                    <textarea
                      value={settings.ai_settings.system_prompt}
                      onChange={(e) => updateAISettings({ system_prompt: e.target.value })}
                      rows={4}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'ui' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">界面设置</h2>
              
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">外观</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      主题
                    </label>
                    <select
                      value={settings.ui_settings.theme}
                      onChange={(e) => updateUISettings({ theme: e.target.value as any })}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="light">浅色</option>
                      <option value="dark">深色</option>
                      <option value="auto">跟随系统</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      字体大小: {settings.ui_settings.font_size}px
                    </label>
                    <input
                      type="range"
                      min="12"
                      max="24"
                      value={settings.ui_settings.font_size}
                      onChange={(e) => updateUISettings({ font_size: parseInt(e.target.value) })}
                      className="w-full"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      快捷键
                    </label>
                    <input
                      type="text"
                      value={settings.ui_settings.hotkey}
                      onChange={(e) => updateUISettings({ hotkey: e.target.value })}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                      placeholder="Ctrl+Q"
                    />
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="animations"
                      checked={settings.ui_settings.animations_enabled}
                      onChange={(e) => updateUISettings({ animations_enabled: e.target.checked })}
                      className="mr-3"
                    />
                    <label htmlFor="animations" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      启用动画效果
                    </label>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'siyuan' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">思源笔记集成</h2>
              
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">连接设置</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      服务器地址
                    </label>
                    <input
                      type="text"
                      value={settings.siyuan_settings.host}
                      onChange={(e) => updateSiYuanSettings({ host: e.target.value })}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                      placeholder="127.0.0.1"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      端口号
                    </label>
                    <input
                      type="number"
                      value={settings.siyuan_settings.port}
                      onChange={(e) => updateSiYuanSettings({ port: parseInt(e.target.value) })}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                      placeholder="6806"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      API Token
                    </label>
                    <input
                      type="password"
                      value={settings.siyuan_settings.token}
                      onChange={(e) => updateSiYuanSettings({ token: e.target.value })}
                      className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
                      placeholder="输入 API Token"
                    />
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="auto-sync"
                      checked={settings.siyuan_settings.auto_sync}
                      onChange={(e) => updateSiYuanSettings({ auto_sync: e.target.checked })}
                      className="mr-3"
                    />
                    <label htmlFor="auto-sync" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      启用自动同步
                    </label>
                  </div>
                  
                  <div className="flex space-x-4">
                    <button className="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                      测试连接
                    </button>
                    <button className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-800 dark:text-white font-medium py-2 px-4 rounded-lg transition-colors">
                      获取笔记本列表
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;
