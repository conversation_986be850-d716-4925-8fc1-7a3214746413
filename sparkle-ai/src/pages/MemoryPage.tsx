import React, { useState } from 'react';
import { useMemory } from '../hooks/useMemory';
import { useNavigate } from 'react-router-dom';



const MemoryPage: React.FC = () => {
  const navigate = useNavigate();
  const {
    memories,
    isLoading,
    error,
    searchQuery,
    selectedMemory,
    handleSearchChange,
    selectMemory,
    clearSelection,
    filterMemories,
    getImportanceLabel,
    addMemory,
    updateMemory,
    deleteMemory
  } = useMemory();

  const [filterImportance, setFilterImportance] = useState<number | null>(null);

  // 过滤记忆
  const filteredMemories = filterMemories(filterImportance || undefined);

  // 格式化日期
  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  // 获取重要性颜色类
  const getImportanceColorClass = (level: number) => {
    const label = getImportanceLabel(level);
    switch (label.color) {
      case 'blue': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'yellow': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      case 'red': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* 记忆列表侧边栏 */}
      <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3 mb-2">
            <button
              onClick={() => navigate('/chat')}
              className="p-1 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              title="返回聊天"
            >
              <span className="text-gray-500 dark:text-gray-400">←</span>
            </button>
            <h1 className="text-xl font-bold text-gray-900 dark:text-white">记忆管理</h1>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            管理 AI 的记忆和知识库
          </p>

          {/* 错误提示 */}
          {error && (
            <div className="mt-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 p-3 rounded-lg">
              <div className="flex items-center">
                <span className="text-red-600 dark:text-red-400 mr-2">⚠️</span>
                <span className="text-red-800 dark:text-red-200 text-sm">{error}</span>
              </div>
            </div>
          )}
        </div>
        
        {/* 搜索和过滤 */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <input
            type="text"
            placeholder="搜索记忆..."
            value={searchQuery}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 dark:bg-gray-700 dark:text-white"
            disabled={isLoading}
          />
          
          <div className="flex items-center space-x-2 mt-3">
            <span className="text-sm text-gray-700 dark:text-gray-300">重要性:</span>
            <button
              onClick={() => setFilterImportance(null)}
              className={`px-2 py-1 text-xs rounded-md ${
                filterImportance === null 
                  ? 'bg-primary-100 text-primary-800 dark:bg-primary-900 dark:text-primary-300' 
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              全部
            </button>
            <button
              onClick={() => setFilterImportance(1)}
              className={`px-2 py-1 text-xs rounded-md ${
                filterImportance === 1 
                  ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300' 
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              一般
            </button>
            <button
              onClick={() => setFilterImportance(2)}
              className={`px-2 py-1 text-xs rounded-md ${
                filterImportance === 2 
                  ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300' 
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              重要
            </button>
            <button
              onClick={() => setFilterImportance(3)}
              className={`px-2 py-1 text-xs rounded-md ${
                filterImportance === 3 
                  ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300' 
                  : 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
              }`}
            >
              关键
            </button>
          </div>
        </div>
        
        {/* 记忆列表 */}
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
              <p className="text-gray-500 dark:text-gray-400 mt-2">加载中...</p>
            </div>
          ) : filteredMemories.length === 0 ? (
            <div className="p-4 text-center text-gray-500 dark:text-gray-400">
              {searchQuery ? '没有找到匹配的记忆' : '暂无记忆数据'}
            </div>
          ) : (
            <div className="divide-y divide-gray-200 dark:divide-gray-700">
              {filteredMemories.map(memory => (
                <div
                  key={memory.id}
                  onClick={() => selectMemory(memory)}
                  className={`p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                    selectedMemory?.id === memory.id ? 'bg-gray-100 dark:bg-gray-700' : ''
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 dark:text-white">
                        {memory.summary}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                        {memory.content}
                      </div>
                    </div>
                    <div className={`text-xs px-2 py-1 rounded-full ${getImportanceColorClass(memory.importance_level)}`}>
                      {getImportanceLabel(memory.importance_level).text}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
                    <div>创建: {formatDate(memory.created_at)}</div>
                    <div>访问次数: {memory.access_count}</div>
                  </div>
                  
                  {memory.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      {memory.tags.map(tag => (
                        <span
                          key={tag}
                          className="text-xs px-2 py-0.5 bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-full"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
        
        {/* 底部按钮 */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <button className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
            添加新记忆
          </button>
        </div>
      </div>

      {/* 记忆详情区域 */}
      <div className="flex-1 flex flex-col">
        {selectedMemory ? (
          <>
            <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {selectedMemory.summary}
                  </h2>
                  <div className="flex items-center space-x-2 mt-1">
                    <span className={`text-xs px-2 py-1 rounded-full ${getImportanceColorClass(selectedMemory.importance_level)}`}>
                      {getImportanceLabel(selectedMemory.importance_level).text}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400">
                      创建于 {formatDate(selectedMemory.created_at)}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <span className="text-gray-500 dark:text-gray-400">✏️</span>
                  </button>
                  <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                    <span className="text-gray-500 dark:text-gray-400">🗑️</span>
                  </button>
                </div>
              </div>
            </div>
            
            <div className="flex-1 overflow-y-auto p-6">
              <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-gray-200 dark:border-gray-700">
                <div className="text-gray-900 dark:text-white whitespace-pre-wrap">
                  {selectedMemory.content}
                </div>
                
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">标签</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedMemory.tags.map(tag => (
                      <span
                        key={tag}
                        className="text-sm px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
                
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">统计信息</h3>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">最后访问</div>
                      <div className="text-gray-900 dark:text-white">{formatDate(selectedMemory.last_accessed)}</div>
                    </div>
                    <div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">访问次数</div>
                      <div className="text-gray-900 dark:text-white">{selectedMemory.access_count}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-6xl mb-4">🧠</div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                选择一条记忆查看详情
              </h3>
              <p className="text-gray-500 dark:text-gray-400 max-w-md">
                从左侧列表中选择一条记忆，或者添加新的记忆。
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MemoryPage;
