import React from 'react';
import { useChatStore } from '../stores/chatStore';
import { useSettingsStore } from '../stores/settingsStore';

const MainPage: React.FC = () => {
  const { currentSession, messages, isLoading } = useChatStore();
  const { settings } = useSettingsStore();

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* 侧边栏 */}
      <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
        <div className="p-4">
          <h1 className="text-xl font-bold text-gray-900 dark:text-white">
            Sparkle-AI
          </h1>
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
            智能系统助手
          </p>
        </div>
        
        {/* 会话列表 */}
        <div className="px-4 pb-4">
          <button className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200">
            新建对话
          </button>
        </div>
        
        <div className="flex-1 overflow-y-auto">
          {/* 会话历史列表 */}
          <div className="px-4 space-y-2">
            {/* 这里将来会显示会话列表 */}
            <div className="p-3 rounded-lg bg-gray-100 dark:bg-gray-700 cursor-pointer hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors">
              <div className="font-medium text-gray-900 dark:text-white text-sm">
                示例对话
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                刚刚
              </div>
            </div>
          </div>
        </div>
        
        {/* 底部设置按钮 */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <button className="w-full text-left p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
            <span className="text-gray-700 dark:text-gray-300">⚙️ 设置</span>
          </button>
        </div>
      </div>

      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col">
        {/* 聊天头部 */}
        <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
                {currentSession?.title || '新对话'}
              </h2>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                模型: {settings.ai_settings.default_model}
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <span className="text-gray-500 dark:text-gray-400">🔄</span>
              </button>
              <button className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">
                <span className="text-gray-500 dark:text-gray-400">🗑️</span>
              </button>
            </div>
          </div>
        </div>

        {/* 消息列表 */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-6xl mb-4">🤖</div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  欢迎使用 Sparkle-AI
                </h3>
                <p className="text-gray-500 dark:text-gray-400 max-w-md">
                  我是您的智能系统助手，可以帮助您进行文件操作、系统查询、网络搜索等各种任务。
                  请在下方输入您的问题开始对话。
                </p>
              </div>
            </div>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${
                  message.role === 'user' ? 'justify-end' : 'justify-start'
                }`}
              >
                <div
                  className={`max-w-3xl p-4 rounded-lg ${
                    message.role === 'user'
                      ? 'bg-primary-600 text-white'
                      : 'bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <div className="whitespace-pre-wrap">
                    {message.content}
                  </div>
                  <div className={`text-xs mt-2 ${
                    message.role === 'user' 
                      ? 'text-primary-100' 
                      : 'text-gray-500 dark:text-gray-400'
                  }`}>
                    {new Date(message.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            ))
          )}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-600"></div>
                  <span className="text-gray-500 dark:text-gray-400">AI 正在思考...</span>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 输入区域 */}
        <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-end space-x-4">
            <div className="flex-1">
              <textarea
                placeholder="输入您的问题..."
                className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                rows={3}
                disabled={isLoading}
              />
            </div>
            <button
              className="bg-primary-600 hover:bg-primary-700 disabled:bg-gray-400 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200"
              disabled={isLoading}
            >
              发送
            </button>
          </div>
          
          <div className="flex items-center justify-between mt-2 text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center space-x-4">
              <span>Ctrl+Enter 发送</span>
              <span>Shift+Enter 换行</span>
            </div>
            <div>
              {settings.ai_settings.max_tokens} tokens 限制
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MainPage;
