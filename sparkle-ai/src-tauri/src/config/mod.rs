use std::collections::HashMap;
use crate::types::*;
use crate::database::Database;

pub struct ConfigManager {
    database: Database,
    cached_settings: Option<AppSettings>,
}

impl ConfigManager {
    pub fn new(database: Database) -> Self {
        Self {
            database,
            cached_settings: None,
        }
    }

    pub async fn get_settings(&mut self) -> AppResult<AppSettings> {
        if let Some(ref settings) = self.cached_settings {
            return Ok(settings.clone());
        }

        let settings = self.load_settings_from_db().await?;
        self.cached_settings = Some(settings.clone());
        Ok(settings)
    }

    pub async fn update_settings(&mut self, settings: AppSettings) -> AppResult<()> {
        // 保存到数据库
        let settings_json = serde_json::to_string(&settings)?;
        self.database.save_config("app_settings", &settings_json).await?;
        
        // 更新缓存
        self.cached_settings = Some(settings);
        
        Ok(())
    }

    pub async fn reset_settings(&mut self) -> AppResult<()> {
        let default_settings = self.get_default_settings();
        self.update_settings(default_settings).await?;
        Ok(())
    }

    async fn load_settings_from_db(&mut self) -> AppResult<AppSettings> {
        if let Some(settings_json) = self.database.get_config("app_settings").await? {
            match serde_json::from_str(&settings_json) {
                Ok(settings) => Ok(settings),
                Err(_) => {
                    // 如果解析失败，返回默认设置
                    Ok(self.get_default_settings())
                }
            }
        } else {
            // 如果没有保存的设置，返回默认设置
            Ok(self.get_default_settings())
        }
    }

    fn get_default_settings(&self) -> AppSettings {
        let mut tool_permissions = HashMap::new();
        tool_permissions.insert("safe_shell".to_string(), true);
        tool_permissions.insert("web_search".to_string(), true);
        tool_permissions.insert("gaode_map".to_string(), true);
        tool_permissions.insert("calendar_manager".to_string(), false);
        tool_permissions.insert("siyuan_notes".to_string(), true);

        let api_keys = HashMap::new();

        AppSettings {
            ai_settings: AISettings {
                default_model: "deepseek-chat".to_string(),
                temperature: 0.7,
                max_tokens: 2000,
                conversation_window: 10,
                system_prompt: "你是 Sparkle-AI，一个智能系统助手。你可以帮助用户进行各种任务，包括文件操作、系统查询、网络搜索等。请用中文回答，保持友好和专业。".to_string(),
                tool_permissions,
                api_keys,
            },
            ui_settings: UISettings {
                theme: "auto".to_string(),
                font_size: 14,
                language: "zh-CN".to_string(),
                hotkey: "Ctrl+Q".to_string(),
                window_opacity: 0.95,
                animations_enabled: true,
            },
            security_settings: SecuritySettings {
                tool_execution_enabled: true,
                command_whitelist: vec![
                    "ls".to_string(), "cat".to_string(), "pwd".to_string(), "whoami".to_string(),
                    "date".to_string(), "uptime".to_string(), "df".to_string(), "free".to_string(),
                    "ps".to_string(), "top".to_string(), "htop".to_string(), "uname".to_string(),
                    "find".to_string(), "grep".to_string(), "head".to_string(), "tail".to_string(),
                    "wc".to_string(), "sort".to_string(),
                ],
                command_blacklist: vec![
                    "rm".to_string(), "rmdir".to_string(), "mv".to_string(), "cp".to_string(),
                    "chmod".to_string(), "chown".to_string(), "sudo".to_string(), "su".to_string(),
                    "passwd".to_string(), "useradd".to_string(), "userdel".to_string(),
                    "systemctl".to_string(), "service".to_string(), "mount".to_string(),
                    "umount".to_string(), "fdisk".to_string(), "mkfs".to_string(),
                    "dd".to_string(), "format".to_string(),
                ],
                max_execution_time: 30000, // 30秒
                sandbox_mode: false,
            },
            performance_settings: PerformanceSettings {
                memory_limit: 512, // MB
                cache_size: 100,   // MB
                concurrent_requests: 3,
                log_level: "info".to_string(),
            },
            siyuan_settings: SiYuanSettings {
                host: "127.0.0.1".to_string(),
                port: 6806,
                token: String::new(),
                default_notebook: None,
                auto_sync: false,
                sync_interval: 300, // 5分钟
            },
        }
    }

    // 获取特定的设置部分
    pub async fn get_ai_settings(&mut self) -> AppResult<AISettings> {
        let settings = self.get_settings().await?;
        Ok(settings.ai_settings)
    }

    pub async fn get_ui_settings(&mut self) -> AppResult<UISettings> {
        let settings = self.get_settings().await?;
        Ok(settings.ui_settings)
    }

    pub async fn get_security_settings(&mut self) -> AppResult<SecuritySettings> {
        let settings = self.get_settings().await?;
        Ok(settings.security_settings)
    }

    pub async fn get_performance_settings(&mut self) -> AppResult<PerformanceSettings> {
        let settings = self.get_settings().await?;
        Ok(settings.performance_settings)
    }

    pub async fn get_siyuan_settings(&mut self) -> AppResult<SiYuanSettings> {
        let settings = self.get_settings().await?;
        Ok(settings.siyuan_settings)
    }

    // 更新特定的设置部分
    pub async fn update_ai_settings(&mut self, ai_settings: AISettings) -> AppResult<()> {
        let mut settings = self.get_settings().await?;
        settings.ai_settings = ai_settings;
        self.update_settings(settings).await
    }

    pub async fn update_ui_settings(&mut self, ui_settings: UISettings) -> AppResult<()> {
        let mut settings = self.get_settings().await?;
        settings.ui_settings = ui_settings;
        self.update_settings(settings).await
    }

    pub async fn update_security_settings(&mut self, security_settings: SecuritySettings) -> AppResult<()> {
        let mut settings = self.get_settings().await?;
        settings.security_settings = security_settings;
        self.update_settings(settings).await
    }

    pub async fn update_performance_settings(&mut self, performance_settings: PerformanceSettings) -> AppResult<()> {
        let mut settings = self.get_settings().await?;
        settings.performance_settings = performance_settings;
        self.update_settings(settings).await
    }

    pub async fn update_siyuan_settings(&mut self, siyuan_settings: SiYuanSettings) -> AppResult<()> {
        let mut settings = self.get_settings().await?;
        settings.siyuan_settings = siyuan_settings;
        self.update_settings(settings).await
    }

    // 验证设置
    pub fn validate_settings(&self, settings: &AppSettings) -> AppResult<()> {
        // 验证 AI 设置
        if settings.ai_settings.temperature < 0.0 || settings.ai_settings.temperature > 2.0 {
            return Err(AppError::InvalidInput("Temperature must be between 0.0 and 2.0".to_string()));
        }

        if settings.ai_settings.max_tokens == 0 || settings.ai_settings.max_tokens > 8000 {
            return Err(AppError::InvalidInput("Max tokens must be between 1 and 8000".to_string()));
        }

        // 验证 UI 设置
        if settings.ui_settings.font_size < 8 || settings.ui_settings.font_size > 32 {
            return Err(AppError::InvalidInput("Font size must be between 8 and 32".to_string()));
        }

        if settings.ui_settings.window_opacity < 0.1 || settings.ui_settings.window_opacity > 1.0 {
            return Err(AppError::InvalidInput("Window opacity must be between 0.1 and 1.0".to_string()));
        }

        // 验证思源笔记设置
        if settings.siyuan_settings.port == 0 || settings.siyuan_settings.port > 65535 {
            return Err(AppError::InvalidInput("Port must be between 1 and 65535".to_string()));
        }

        Ok(())
    }
}
