use tauri::State;
use chrono::Utc;
use uuid::Uuid;
use crate::types::*;
use crate::api::AppState;

pub async fn send_message(
    message: String,
    model: Option<String>,
    state: State<'_, AppState>,
) -> AppResult<ChatResponse> {
    // 创建用户消息
    let user_message = ChatMessage {
        id: Uuid::new_v4().to_string(),
        role: "user".to_string(),
        content: message.clone(),
        timestamp: Utc::now(),
        model_used: None,
        tool_calls: None,
        rendered_content: None,
    };

    // 保存用户消息到数据库
    {
        let database = state.database.lock().unwrap().clone();
        database.save_message(&user_message).await?;
    }

    // TODO: 这里应该调用 AI 模型进行处理
    // 现在先返回一个模拟的响应
    let ai_response = format!("收到您的消息：{}", message);
    
    let assistant_message = ChatMessage {
        id: Uuid::new_v4().to_string(),
        role: "assistant".to_string(),
        content: ai_response.clone(),
        timestamp: Utc::now(),
        model_used: model.clone(),
        tool_calls: None,
        rendered_content: None,
    };

    // 保存 AI 响应到数据库
    {
        let database = state.database.lock().unwrap().clone();
        database.save_message(&assistant_message).await?;
    }

    Ok(ChatResponse {
        message: ai_response,
        tool_results: None,
        memories_used: Vec::new(),
        tokens_used: Some(50), // 模拟的 token 使用量
        cost: Some(0.001),     // 模拟的成本
    })
}

pub async fn get_chat_history(
    limit: Option<usize>,
    state: State<'_, AppState>,
) -> AppResult<Vec<ChatMessage>> {
    let database = state.database.lock().unwrap().clone();
    database.get_chat_history(limit).await
}

pub async fn clear_chat_history(
    state: State<'_, AppState>,
) -> AppResult<()> {
    let database = state.database.lock().unwrap().clone();
    database.clear_chat_history().await
}
