use tauri::State;
use crate::types::*;
use crate::api::AppState;

pub async fn execute_tool(
    tool_name: String,
    params: serde_json::Value,
    state: State<'_, AppState>,
) -> AppResult<ToolResult> {
    // TODO: 实现工具执行逻辑
    // 现在返回一个模拟的结果
    
    let start_time = std::time::Instant::now();
    
    let result = match tool_name.as_str() {
        "safe_shell" => {
            // 模拟 shell 命令执行
            "Command executed successfully".to_string()
        }
        "web_search" => {
            // 模拟网络搜索
            "Search results found".to_string()
        }
        "gaode_map" => {
            // 模拟地图查询
            "Location information retrieved".to_string()
        }
        _ => {
            return Err(AppError::ToolExecution(format!("Unknown tool: {}", tool_name)));
        }
    };
    
    let execution_time = start_time.elapsed().as_millis() as u64;
    
    let tool_result = ToolResult {
        tool_name: tool_name.clone(),
        success: true,
        result,
        execution_time,
        error: None,
    };
    
    // 记录工具使用
    {
        let database = state.database.lock().unwrap();
        database.save_tool_usage(&tool_name, &params, &tool_result).await?;
    }
    
    Ok(tool_result)
}

pub async fn get_available_tools(
    state: State<'_, AppState>,
) -> AppResult<Vec<ToolInfo>> {
    // 获取工具权限设置
    let mut config_manager = state.config_manager.lock().unwrap();
    let ai_settings = config_manager.get_ai_settings().await?;
    
    let mut tools = Vec::new();
    
    // 定义可用工具
    let tool_definitions = vec![
        ToolInfo {
            name: "safe_shell".to_string(),
            description: "安全执行 Shell 命令".to_string(),
            parameters: serde_json::json!({
                "type": "object",
                "properties": {
                    "command": {
                        "type": "string",
                        "description": "要执行的命令"
                    }
                },
                "required": ["command"]
            }),
            enabled: ai_settings.tool_permissions.get("safe_shell").copied().unwrap_or(false),
        },
        ToolInfo {
            name: "web_search".to_string(),
            description: "网络搜索".to_string(),
            parameters: serde_json::json!({
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "搜索关键词"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "结果数量限制",
                        "default": 10
                    }
                },
                "required": ["query"]
            }),
            enabled: ai_settings.tool_permissions.get("web_search").copied().unwrap_or(false),
        },
        ToolInfo {
            name: "gaode_map".to_string(),
            description: "高德地图查询".to_string(),
            parameters: serde_json::json!({
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "位置名称"
                    },
                    "type": {
                        "type": "string",
                        "description": "查询类型：weather, location, nearby",
                        "default": "location"
                    }
                },
                "required": ["location"]
            }),
            enabled: ai_settings.tool_permissions.get("gaode_map").copied().unwrap_or(false),
        },
        ToolInfo {
            name: "siyuan_notes".to_string(),
            description: "思源笔记操作".to_string(),
            parameters: serde_json::json!({
                "type": "object",
                "properties": {
                    "action": {
                        "type": "string",
                        "description": "操作类型：create, search, update, delete"
                    },
                    "title": {
                        "type": "string",
                        "description": "笔记标题"
                    },
                    "content": {
                        "type": "string",
                        "description": "笔记内容"
                    },
                    "query": {
                        "type": "string",
                        "description": "搜索关键词"
                    }
                },
                "required": ["action"]
            }),
            enabled: ai_settings.tool_permissions.get("siyuan_notes").copied().unwrap_or(false),
        },
    ];
    
    tools.extend(tool_definitions);
    
    Ok(tools)
}
