use tauri::State;
use crate::types::*;
use crate::api::AppState;

pub async fn get_settings(
    state: State<'_, AppState>,
) -> AppResult<AppSettings> {
    let mut config_manager = state.config_manager.lock().unwrap().clone();
    config_manager.get_settings().await
}

pub async fn update_settings(
    settings: AppSettings,
    state: State<'_, AppState>,
) -> AppResult<()> {
    let mut config_manager = state.config_manager.lock().unwrap().clone();

    // 验证设置
    config_manager.validate_settings(&settings)?;

    // 更新设置
    config_manager.update_settings(settings).await
}

pub async fn reset_settings(
    state: State<'_, AppState>,
) -> AppResult<()> {
    let mut config_manager = state.config_manager.lock().unwrap().clone();
    config_manager.reset_settings().await
}
