use tauri::State;
use chrono::Utc;
use uuid::Uuid;
use crate::types::*;
use crate::api::AppState;

pub async fn search_memories(
    query: String,
    limit: Option<usize>,
    state: State<'_, AppState>,
) -> AppResult<Vec<Memory>> {
    let database = state.database.lock().unwrap().clone();
    database.search_memories(&query, limit).await
}

pub async fn add_memory(
    content: String,
    importance: u8,
    state: State<'_, AppState>,
) -> AppResult<Memory> {
    let memory = Memory {
        id: Uuid::new_v4().to_string(),
        content: content.clone(),
        summary: None, // TODO: 使用 AI 生成摘要
        importance_level: importance,
        created_at: Utc::now(),
        last_accessed: Utc::now(),
        access_count: 0,
        tags: Vec::new(), // TODO: 使用 AI 生成标签
    };

    let database = state.database.lock().unwrap().clone();
    database.save_memory(&memory).await?;

    Ok(memory)
}

pub async fn update_memory(
    id: String,
    content: String,
    importance: u8,
    state: State<'_, AppState>,
) -> AppResult<()> {
    let database = state.database.lock().unwrap().clone();
    database.update_memory(&id, &content, importance).await
}

pub async fn delete_memory(
    id: String,
    state: State<'_, AppState>,
) -> AppResult<()> {
    let database = state.database.lock().unwrap().clone();
    database.delete_memory(&id).await
}
