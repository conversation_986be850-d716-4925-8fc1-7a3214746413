use sqlx::{SqlitePool, Row};
use chrono::{DateTime, Utc};
use crate::types::*;

pub mod migrations;

#[derive(Clone)]
pub struct Database {
    pool: SqlitePool,
}

impl Database {
    pub async fn new(database_url: &str) -> AppResult<Self> {
        let pool = SqlitePool::connect(database_url).await?;

        // 运行迁移
        migrations::run_migrations(&pool).await?;

        Ok(Database { pool })
    }

    // 聊天消息相关操作
    pub async fn save_message(&self, message: &ChatMessage) -> AppResult<()> {
        sqlx::query(
            r#"
            INSERT INTO conversations (id, session_id, role, content, tool_calls, timestamp, model_used)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&message.id)
        .bind("default") // 暂时使用默认会话ID
        .bind(&message.role)
        .bind(&message.content)
        .bind(serde_json::to_string(&message.tool_calls).unwrap_or_default())
        .bind(&message.timestamp.to_rfc3339())
        .bind(&message.model_used)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_chat_history(&self, limit: Option<usize>) -> AppResult<Vec<ChatMessage>> {
        // 暂时返回空列表，避免编译错误
        Ok(Vec::new())
    }

    pub async fn clear_chat_history(&self) -> AppResult<()> {
        sqlx::query("DELETE FROM conversations")
            .execute(&self.pool)
            .await?;
        Ok(())
    }

    // 记忆相关操作
    pub async fn save_memory(&self, memory: &Memory) -> AppResult<()> {
        // 暂时简化实现
        Ok(())
    }

    pub async fn search_memories(&self, query: &str, limit: Option<usize>) -> AppResult<Vec<Memory>> {
        // 暂时返回空列表
        Ok(Vec::new())
    }

    pub async fn update_memory(&self, id: &str, content: &str, importance: u8) -> AppResult<()> {
        // 暂时简化实现
        Ok(())
    }

    pub async fn delete_memory(&self, id: &str) -> AppResult<()> {
        // 暂时简化实现
        Ok(())
    }

    // 工具使用记录
    pub async fn save_tool_usage(&self, tool_name: &str, params: &str, result: &ToolResult) -> AppResult<()> {
        // 暂时简化实现
        Ok(())
    }

    // 配置相关
    pub async fn save_config(&self, key: &str, value: &str) -> AppResult<()> {
        // 暂时简化实现
        Ok(())
    }

    pub async fn get_config(&self, key: &str) -> AppResult<Option<String>> {
        // 暂时返回 None
        Ok(None)
    }

    // 思源笔记配置
    pub async fn save_siyuan_config(&self, host: &str, port: u16, token: &str) -> AppResult<()> {
        // 暂时简化实现
        Ok(())
    }

    pub async fn get_siyuan_config(&self) -> AppResult<Option<SiYuanSettings>> {
        // 暂时返回 None
        Ok(None)
    }
}
