use sqlx::{SqlitePool, Row};
use chrono::{DateTime, Utc};
use crate::types::*;

pub mod migrations;

#[derive(Clone)]
pub struct Database {
    pool: SqlitePool,
}

impl Database {
    pub async fn new(database_url: &str) -> AppResult<Self> {
        let pool = SqlitePool::connect(database_url).await?;
        
        // 运行迁移
        migrations::run_migrations(&pool).await?;
        
        Ok(Database { pool })
    }

    // 聊天消息相关操作
    pub async fn save_message(&self, message: &ChatMessage) -> AppResult<()> {
        sqlx::query!(
            r#"
            INSERT INTO conversations (id, session_id, role, content, tool_calls, timestamp, model_used)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            "#,
            message.id,
            "default", // 暂时使用默认会话ID
            message.role,
            message.content,
            serde_json::to_string(&message.tool_calls)?,
            message.timestamp,
            message.model_used
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_chat_history(&self, limit: Option<usize>) -> AppResult<Vec<ChatMessage>> {
        let limit = limit.unwrap_or(50) as i64;
        
        let rows = sqlx::query!(
            r#"
            SELECT id, role, content, tool_calls, timestamp, model_used
            FROM conversations
            ORDER BY timestamp DESC
            LIMIT ?
            "#,
            limit
        )
        .fetch_all(&self.pool)
        .await?;

        let mut messages = Vec::new();
        for row in rows {
            let tool_calls: Option<Vec<ToolCall>> = if let Some(tc) = row.tool_calls {
                serde_json::from_str(&tc).ok()
            } else {
                None
            };

            messages.push(ChatMessage {
                id: row.id,
                role: row.role,
                content: row.content,
                timestamp: DateTime::parse_from_rfc3339(&row.timestamp)
                    .map_err(|e| AppError::Unknown(e.to_string()))?
                    .with_timezone(&Utc),
                model_used: row.model_used,
                tool_calls,
                rendered_content: None,
            });
        }

        // 反转顺序，使最新的消息在最后
        messages.reverse();
        Ok(messages)
    }

    pub async fn clear_chat_history(&self) -> AppResult<()> {
        sqlx::query!("DELETE FROM conversations")
            .execute(&self.pool)
            .await?;
        Ok(())
    }

    // 记忆相关操作
    pub async fn save_memory(&self, memory: &Memory) -> AppResult<()> {
        sqlx::query!(
            r#"
            INSERT INTO memories (id, content, summary, importance_level, created_at, last_accessed, access_count, tags, source_type)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            memory.id,
            memory.content,
            memory.summary,
            memory.importance_level as i64,
            memory.created_at,
            memory.last_accessed,
            memory.access_count as i64,
            serde_json::to_string(&memory.tags)?,
            "chat"
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn search_memories(&self, query: &str, limit: Option<usize>) -> AppResult<Vec<Memory>> {
        let limit = limit.unwrap_or(10) as i64;
        
        let rows = sqlx::query!(
            r#"
            SELECT id, content, summary, importance_level, created_at, last_accessed, access_count, tags
            FROM memories
            WHERE content LIKE ? OR summary LIKE ?
            ORDER BY importance_level DESC, last_accessed DESC
            LIMIT ?
            "#,
            format!("%{}%", query),
            format!("%{}%", query),
            limit
        )
        .fetch_all(&self.pool)
        .await?;

        let mut memories = Vec::new();
        for row in rows {
            let tags: Vec<String> = if let Some(t) = row.tags {
                serde_json::from_str(&t).unwrap_or_default()
            } else {
                Vec::new()
            };

            memories.push(Memory {
                id: row.id,
                content: row.content,
                summary: row.summary,
                importance_level: row.importance_level as u8,
                created_at: DateTime::parse_from_rfc3339(&row.created_at)
                    .map_err(|e| AppError::Unknown(e.to_string()))?
                    .with_timezone(&Utc),
                last_accessed: DateTime::parse_from_rfc3339(&row.last_accessed)
                    .map_err(|e| AppError::Unknown(e.to_string()))?
                    .with_timezone(&Utc),
                access_count: row.access_count as u32,
                tags,
            });
        }

        Ok(memories)
    }

    pub async fn update_memory(&self, id: &str, content: &str, importance: u8) -> AppResult<()> {
        sqlx::query!(
            r#"
            UPDATE memories
            SET content = ?, importance_level = ?, last_accessed = ?
            WHERE id = ?
            "#,
            content,
            importance as i64,
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn delete_memory(&self, id: &str) -> AppResult<()> {
        sqlx::query!("DELETE FROM memories WHERE id = ?", id)
            .execute(&self.pool)
            .await?;
        Ok(())
    }

    // 工具使用记录
    pub async fn save_tool_usage(&self, tool_name: &str, parameters: &serde_json::Value, result: &ToolResult) -> AppResult<()> {
        sqlx::query!(
            r#"
            INSERT INTO tool_usage (tool_name, parameters, result, success, execution_time, timestamp)
            VALUES (?, ?, ?, ?, ?, ?)
            "#,
            tool_name,
            serde_json::to_string(parameters)?,
            result.result,
            result.success,
            result.execution_time as i64,
            Utc::now()
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    // 配置相关操作
    pub async fn save_config(&self, key: &str, value: &str) -> AppResult<()> {
        sqlx::query!(
            r#"
            INSERT OR REPLACE INTO user_config (key, value, updated_at)
            VALUES (?, ?, ?)
            "#,
            key,
            value,
            Utc::now()
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_config(&self, key: &str) -> AppResult<Option<String>> {
        let row = sqlx::query!(
            "SELECT value FROM user_config WHERE key = ?",
            key
        )
        .fetch_optional(&self.pool)
        .await?;

        Ok(row.map(|r| r.value))
    }

    // 思源笔记配置
    pub async fn save_siyuan_config(&self, config: &SiYuanSettings) -> AppResult<()> {
        sqlx::query!(
            r#"
            INSERT OR REPLACE INTO siyuan_config (id, name, host, port, token, is_active, created_at, updated_at)
            VALUES (1, 'default', ?, ?, ?, 1, ?, ?)
            "#,
            config.host,
            config.port as i64,
            config.token,
            Utc::now(),
            Utc::now()
        )
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_siyuan_config(&self) -> AppResult<Option<SiYuanSettings>> {
        let row = sqlx::query!(
            "SELECT host, port, token FROM siyuan_config WHERE is_active = 1 LIMIT 1"
        )
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row {
            Ok(Some(SiYuanSettings {
                host: row.host,
                port: row.port as u16,
                token: row.token.unwrap_or_default(),
                default_notebook: None,
                auto_sync: false,
                sync_interval: 300,
            }))
        } else {
            Ok(None)
        }
    }
}
