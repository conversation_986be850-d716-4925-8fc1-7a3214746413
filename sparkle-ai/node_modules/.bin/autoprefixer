#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/../.store/autoprefixer@10.4.21/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/../.store/autoprefixer@10.4.21/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/autoprefixer@10.4.21/node_modules/autoprefixer/bin/autoprefixer" "$@"
else
  exec node  "$basedir/../.store/autoprefixer@10.4.21/node_modules/autoprefixer/bin/autoprefixer" "$@"
fi
