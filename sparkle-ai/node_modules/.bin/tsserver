#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/../.store/typescript@5.6.3/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/../.store/typescript@5.6.3/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/typescript@5.6.3/node_modules/typescript/bin/tsserver" "$@"
else
  exec node  "$basedir/../.store/typescript@5.6.3/node_modules/typescript/bin/tsserver" "$@"
fi
