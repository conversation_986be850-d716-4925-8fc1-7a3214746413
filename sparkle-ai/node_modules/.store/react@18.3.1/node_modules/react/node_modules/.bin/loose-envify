#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/../../../../../loose-envify@1.4.0/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/../../../../../loose-envify@1.4.0/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../loose-envify@1.4.0/node_modules/loose-envify/cli.js" "$@"
else
  exec node  "$basedir/../../../../../loose-envify@1.4.0/node_modules/loose-envify/cli.js" "$@"
fi
