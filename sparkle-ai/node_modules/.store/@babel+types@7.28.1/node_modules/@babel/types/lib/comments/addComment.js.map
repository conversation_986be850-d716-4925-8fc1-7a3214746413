{"version": 3, "names": ["_addComments", "require", "addComment", "node", "type", "content", "line", "addComments", "value"], "sources": ["../../src/comments/addComment.ts"], "sourcesContent": ["import addComments from \"./addComments.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Add comment of certain type to a node.\n */\nexport default function addComment<T extends t.Node>(\n  node: T,\n  type: t.CommentTypeShorthand,\n  content: string,\n  line?: boolean,\n): T {\n  return addComments(node, type, [\n    {\n      type: line ? \"CommentLine\" : \"CommentBlock\",\n      value: content,\n    } as t.Comment,\n  ]);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAMe,SAASC,UAAUA,CAChCC,IAAO,EACPC,IAA4B,EAC5BC,OAAe,EACfC,IAAc,EACX;EACH,OAAO,IAAAC,oBAAW,EAACJ,IAAI,EAAEC,IAAI,EAAE,CAC7B;IACEA,IAAI,EAAEE,IAAI,GAAG,aAAa,GAAG,cAAc;IAC3CE,KAAK,EAAEH;EACT,CAAC,CACF,CAAC;AACJ", "ignoreList": []}